// CSS Variables
:root {
  --primary-color: #0066b1;
  --modal-background: #003963;
  --text-primary: #ffffff;
  --text-secondary: #C5E6FF;
  --overlay-background: rgba(0, 14, 26, 0.9);
}

/* ===== MODAL LAYOUT ===== */
.settings-modal-overlay {
  position: fixed;
  inset: 0;
  background: var(--overlay-background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.settings-modal {
  position: relative;
  width: 800px;
  display: flex;
  flex-direction: column;
  background: var(--modal-background);
  border-radius: 32px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
  overflow: hidden;
}

/* ===== HEADER COMPONENTS ===== */
.settings-modal__close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    opacity: 0.8;
  }

  &:focus-visible {
    outline: 2px solid var(--text-primary);
    outline-offset: 2px;
  }
}

.settings-modal__title {
  font-family: var(--font-sofia-pro);
  font-size: 48px;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.384px;
  color: var(--text-primary);
  margin: 0 0 32px 0;
}

/* ===== CONTENT SECTIONS ===== */
.settings-modal__content {
  flex: 1;
  padding: 32px 48px 16px 48px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.settings-modal__temperature-section {
  width: 100%;
}

.settings-modal__public-message {
  width: 100%;
  text-align: center;
  padding: 32px 0;

  p {
    color: var(--text-secondary);
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 130%;
    margin: 0;
  }
}

.settings-modal__temperature-title {
  font-family: var(--font-sofia-pro);
  font-size: 16px;
  font-weight: 700;
  line-height: 130%;
  letter-spacing: 0.128px;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.settings-modal__temperature-description {
  width: 450px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  flex: 1 0 0;
  overflow: hidden;
  color: var(--text-secondary);
  text-overflow: ellipsis;
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: 0.096px;
  margin: 0 0 16px 0;
}

/* ===== RADIO COMPONENTS ===== */
.settings-modal__radio-group {
  height: 44px;
  padding: 4px;
  display: flex;
  align-items: center;
  gap: 24px;
  margin: 0 0 32px 0;
}

.settings-modal__radio-option {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  color: var(--text-primary);
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  cursor: pointer;
}

.settings-modal__radio-input {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px solid var(--text-primary);
  background-color: transparent;
  transition: background-color 0.2s ease;

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.settings-modal__radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] {
    display: none;
  }

  &::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--text-primary);
  }
}

/* ===== ACTIONS ===== */
.settings-modal__actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}

.settings-modal__button {
  display: flex;
  align-items: center;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  outline: none;

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  &--cancel {
    background: transparent;
    color: var(--text-primary);
    padding: 8px 16px;

    &:hover {
      opacity: 0.8;
    }
  }

  &--save {
    gap: 4px;
    padding: 12px 24px;
    background: var(--primary-color);
    border-radius: 50px;
    color: var(--text-primary);

    &:hover {
      background-color: #0052a3;
    }
  }
}

