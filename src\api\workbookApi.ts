import type {
  RetrievalFileSignedUrlUpload,
  RetrievalFileSignedUrlUploadResponse,
  RetrievalFileSignedUrlUploadBatchResponse,
  RetrievalWorkbook,
  RetrievalWorkbookCreate,
  RetrievalWorkbookCreateResponse,
  RetrievalWorkbookUpdate,
  RetrievalWorkbookUpdateResponse,
  RetrievalFileCallbackResponse,
  RetrievalFilesCallbackResponse,
  RetrievalFileIndexStatusResponse,
  RetrievalWorkbookQueryResponse,
  RetrievalWorkbookQueryRequest,
  RetrievalFileDeleteResponse,
  RetrievalWorkbookMessageFeedback,
  RetrievalSessionCreateResponse,
} from '@features/workbook/workbookTypes';

import { client } from './client';

const { hostname, pathname } = window.location;
const baseHost = hostname.includes('localhost') ? 'http://localhost:5000' : ``;

let baseApiPath = '/sidekick';
const apiPathPrefix = pathname.split(baseApiPath)[0];
baseApiPath = `${apiPathPrefix}${baseApiPath}/api/workbooks`;

const baseRoute = `${baseHost}${baseApiPath}`;
const baseRoutes = {
  USER_WORKBOOKS: `${baseRoute}/my`,
  GLOBAL_WORKBOOKS: `${baseRoute}/public`,
};

export interface GetWorkbooksResponse {
  user: string | null;
  workbooks: RetrievalWorkbook[];
}

const getWorkbooks = async (isGlobal: boolean = true) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.get<GetWorkbooksResponse>(baseRoute);
  return response.data;
};

const getWorkbookById = async (workbookId: string, isGlobal: boolean = true) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.get<RetrievalWorkbook>(`${baseRoute}/${workbookId}`);
  return response.data;
};

export const postWorkbook = async (toCreate: RetrievalWorkbookCreate) => {
  const response = await client.post<RetrievalWorkbookCreateResponse>(`${baseRoutes.USER_WORKBOOKS}`, toCreate);
  return response.data;
};

export const postPublicWorkbook = async (toCreate: RetrievalWorkbookCreate) => {
  const response = await client.post<RetrievalWorkbookCreateResponse>(`${baseRoutes.GLOBAL_WORKBOOKS}`, toCreate);
  return response.data;
};

export const postSession = async (workbookId: string, isGlobal: boolean = false) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.post<RetrievalSessionCreateResponse>(`${baseRoute}/${workbookId}/sessions`, {});
  return response.data;
};

export const postWorkbookFiles = async (
  workbookId: string,
  filesToSign: RetrievalFileSignedUrlUpload,
  isGlobal: boolean = false
) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.post<RetrievalFileSignedUrlUploadBatchResponse>(
    `${baseRoute}/${workbookId}/files/sign`,
    {
      filesToSign: filesToSign,
    }
  );
  return response.data;
};

export const putFileDirectly = async (
  file: File,
  signedFileInfo: RetrievalFileSignedUrlUploadResponse
): Promise<readonly [RetrievalFileSignedUrlUploadResponse, boolean]> => {
  const uploadResponse: Response = await fetch(signedFileInfo.signedUrl, {
    method: 'PUT',
    headers: { 'Content-Type': signedFileInfo.mimeType },
    body: file,
  });

  if (!uploadResponse.ok) {
    console.error(`Failed to upload ${signedFileInfo.name}: (${uploadResponse.status}) ${uploadResponse.statusText}`);
  }

  return [signedFileInfo, uploadResponse.ok];
};

export const postFilesCallback = async (
  workbookId: string,
  fileInfo: RetrievalFileSignedUrlUploadResponse[],
  isGlobal: boolean = false
): Promise<RetrievalFilesCallbackResponse> => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const requestBody = {
    uploadedFiles: fileInfo,
  };
  const response = await client.post<RetrievalFilesCallbackResponse>(
    `${baseRoute}/${workbookId}/files/uploaded`,
    requestBody
  );
  return response.data;
};

export const postFileCallback = async (
  workbookId: string,
  fileInfo: RetrievalFileSignedUrlUploadResponse,
  isGlobal: boolean = false
): Promise<RetrievalFileCallbackResponse> => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const requestBody = {
    uploadedFile: fileInfo,
  };
  const response = await client.put<RetrievalFileCallbackResponse>(
    `${baseRoute}/${workbookId}/file/${encodeURIComponent(fileInfo.name)}/uploaded`,
    requestBody
  );
  return response.data;
};

export const getFileIndexStatus = async (
  workbookId: string,
  fileId: string,
  isGlobal: boolean = false
): Promise<RetrievalFileIndexStatusResponse> => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.get<RetrievalFileIndexStatusResponse>(
    `${baseRoute}/${workbookId}/file/${fileId}/index_status`
  );
  return response.data;
};

export const getUserWorkbooks = async () => {
  return await getWorkbooks(false);
};

export const getGlobalWorkbooks = async () => {
  return await getWorkbooks(true);
};

export const getUserWorkbookById = async (workbookId: string) => {
  return await getWorkbookById(workbookId, false);
};

export const getGlobalWorkbookById = async (workbookId: string) => {
  return await getWorkbookById(workbookId, true);
};

export const patchWorkbookById = async (workbookUpdates: RetrievalWorkbookUpdate, isGlobal: boolean = false) => {
  const { id, ...updates } = workbookUpdates;

  // Dynamically build the request body with only provided values
  const requestBody: {
    name?: string;
    description?: string | null;
    temperature?: number;
    clear_description?: boolean;
  } = {};

  if (updates.name !== undefined) {
    requestBody.name = updates.name;
  }
  if (updates.description !== undefined) {
    requestBody.description = updates.description;
  } else if (updates.clear_description) {
    requestBody.description = null; // Explicitly clear
  }
  if (updates.temperature !== undefined) {
    requestBody.temperature = updates.temperature;
  }
  if (updates.clear_description !== undefined) {
    requestBody.clear_description = updates.clear_description;
  }

  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.patch<RetrievalWorkbookUpdateResponse>(`${baseRoute}/${id}`, requestBody);
  return response.data;
};

export const deleteWorkbookById = async (workbookId: string, isGlobal: boolean = false) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.delete(`${baseRoute}/${workbookId}`);
  return response.data;
};

export const deleteWorkbookFileById = async (workbookId: string, fileId: string, isGlobal: boolean = false) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.delete<RetrievalFileDeleteResponse>(`${baseRoute}/${workbookId}/files/${fileId}`);
  return response.data;
};

export const queryWorkbookById = async (
  workbookId: string,
  workbookQueryRequest: RetrievalWorkbookQueryRequest,
  isGlobal: boolean = true
) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  const response = await client.post<RetrievalWorkbookQueryResponse>(
    `${baseRoute}/${workbookId}/ask`,
    workbookQueryRequest
  );
  return response.data;
};

export const queryUserWorkbookById = async (
  workbookId: string,
  workbookQueryRequest: RetrievalWorkbookQueryRequest
) => {
  return await queryWorkbookById(workbookId, workbookQueryRequest, false);
};

export const queryGlobalWorkbookById = async (
  workbookId: string,
  workbookQueryRequest: RetrievalWorkbookQueryRequest
) => {
  return await queryWorkbookById(workbookId, workbookQueryRequest, true);
};

export const sendFeedbackByMessageId = async (
  workbookId: string,
  sessionId: string,
  messageId: string,
  feedback: 0 | 1, //0 for thumbsDown, 1 for thumbsUp
  isGlobal: boolean = true
) => {
  const baseRoute = isGlobal ? baseRoutes.GLOBAL_WORKBOOKS : baseRoutes.USER_WORKBOOKS;
  return await client.post<RetrievalWorkbookMessageFeedback>(
    `${baseRoute}/${workbookId}/sessions/${sessionId}/feedback/${messageId}/${feedback}`,
    {}
  );
};
