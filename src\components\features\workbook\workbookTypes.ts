import { ReactNode } from 'react';

export interface Workbook {
  id: string;
  title: string;
  lastEdited: Date;
  fileName?: string;
  files?: string[];
  isPersonal?: boolean;
  isPublic?: boolean;
}

export interface WorkbookDashboardData {
  id: string;
  title: string;
  lastEdited: Date;
  files: string[];
  // Extended fields
  description?: string;
  createdBy?: string;
  isPublic?: boolean;
  isPersonal?: boolean;
}

export enum RetrievalWorkbookChunkSize {
  Small = 100,
  Medium = 250,
  Large = 500,
}

export interface RetrievalWorkbook {
  id: string;
  name: string;
  author: string;
  description?: string;
  systemInstructions?: string;
  dataStoreId: string;
  clonedFrom?: string;
  numFiles?: number;
  chunkSize: RetrievalWorkbookChunkSize;
  temperature: number;
  createdUtc: Date;
  updatedUtc: Date;
  tags: string[];
  authorizedEntities: string[];
  files: RetrievalFile[];
  sessions: RetrievalSession[];
}

export interface RetrievalFile {
  id: string;
  name: string;
  mimeType: string;
  gcsPath: string;
  fileSizeBytes: number;
  tokenCount: number;
  dataStoreId: string;
  chunkSize: RetrievalWorkbookChunkSize;
  isChunked: boolean;
  createdUtc: Date | string;
  updatedUtc: Date | string;
}

export interface RetrievalSession {
  id: string;
  user_id: string;
  messages: (RetrievalSessionQuery | RetrievalSessionAnswer)[];
  createdUtc?: Date | string;
}

export interface RetrievalSessionMessage {
  id: string;
  createdUtc: Date | string;
  conversationRole: 'user' | 'model';
}

export interface RetrievalSessionQuery extends RetrievalSessionMessage {
  query: string;
  conversationRole: 'user';
}

export interface RetrievalSessionAnswer extends RetrievalSessionMessage {
  answer: string;
  conversationRole: 'model';
  state: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED' | null;
  citations: RetrievalCitation[];
  citationSources: RetrievalCitationSource[];
  feedback?: 0 | 1 | null;
}

export interface RetrievalCitation {
  start_index: number;
  end_index: number;
  citation_sources: number[];
  support_score?: number;
}

export interface RetrievalCitationSource {
  file_id: string;
  name: string;
  gcs_path: string;
  chunk_content: string;
}

export type RetrievalWorkbookCreate = Pick<
  RetrievalWorkbook,
  'name' | 'description' | 'systemInstructions' | 'chunkSize' | 'temperature'
>;
export type RetrievalWorkbookCreateResponse = {
  workbook: RetrievalWorkbook;
};

export type RetrievalWorkbookUpdate = Pick<RetrievalWorkbook, 'id'> & {
  name?: string;
  description?: string;
  temperature?: number;
  clear_description?: boolean;
};
export type RetrievalWorkbookUpdateResponse = Pick<
  RetrievalWorkbook,
  'id' | 'name' | 'description' | 'systemInstructions' | 'temperature' | 'updatedUtc'
>;

export type RetrievalSessionCreateResponse = {
  workbookId: string;
  session: RetrievalSession;
};

export type RetrievalFileCallbackResponse = {
  workbookId: string;
  file: RetrievalFile;
};

export type RetrievalFilesCallbackResponse = {
  workbookId: string;
  files: RetrievalFile[];
};

export type RetrievalFileIndexStatusResponse = {
  ready: boolean;
  file: RetrievalFile;
};

export type RetrievalFileSignedUrlUpload = Pick<RetrievalFile, 'name' | 'mimeType' | 'fileSizeBytes'>[];

export type RetrievalFileSignedUrlUploadResponse = Pick<
  RetrievalFile,
  'name' | 'mimeType' | 'fileSizeBytes' | 'gcsPath'
> & {
  signedUrl: string;
};

export type RetrievalFileSignedUrlUploadBatchResponse = {
  filesSignedUrls: RetrievalFileSignedUrlUploadResponse[];
};

export type RetrievalFileDeleteResponse = {
  workbookId: string;
  fileId: string;
};

// =========== Context Types ===========

/**
 * Interface for WorkbookContext
 */
export interface WorkbookContextType {
  workbooks: Workbook[];
  loading: boolean;
  error: string | null;
  currentWorkbook: WorkbookDashboardData | null;
  setCurrentWorkbookById: (id: string) => void;
  clearCurrentWorkbook: () => void;
  resetWorkbookState: () => void;
}

/**
 * Props for WorkbookProvider component
 */
export interface WorkbookProviderProps {
  children: ReactNode;
  initialWorkbooks?: Workbook[];
}

// =========== Hook Types ===========

/**
 * Props for useWorkbookDashboard hook
 */
export interface UseWorkbookDashboardProps {
  initialFiles?: string[];
  onSendMessage?: (message: string) => void;
  onDeleteFile?: (index: number) => void;
}

// =========== Common Component Props ===========

/**
 * Props for Modal component
 */
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

// List View Components
export interface WorkbookCardProps {
  workbook: RetrievalWorkbook;
  navigateTo?: string;
  isGlobal?: boolean;
}

export interface WorkbookContainerProps {
  children: ReactNode;
}

export interface AddWorkbookCardProps {
  onClick: () => void;
  isGlobal: boolean;
}

export interface WorkbookListViewProps {
  isGlobal: boolean;
}

export interface WorkbookDetailsViewProps {
  isGlobal: boolean;
}

// Dashboard View Components
export interface WorkbookDashboardViewProps {
  onSendMessage?: (message: string) => void;
}

export interface GreetingSectionProps {
  textColor: string;
}

export interface InputLowerTrayProps {
  onSubmit: () => void;
  textColor?: string;
}

// File Components
export interface FileCardProps {
  fileName: string;
  onDelete: () => void;
}

export interface FileAreaProps {
  files: string[];
  onDelete: (index: number) => void;
}

export interface RightPanelProps {
  files: string[];
  onDelete?: (index: number) => void;
  onDownload?: (index: number) => void;
}

export interface SearchBarProps {
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus: () => void;
  onBlur: () => void;
}

export interface FileListProps {
  files: string[];
  onDelete?: (index: number) => void;
  onDownload?: (index: number) => void;
}

export interface FileListItemProps {
  fileName: string;
  onDelete?: () => void;
  onDownload?: () => void;
}

// =========== Chat and Message Types ===========

/**
 * Formatted chat message for conversation display
 */
export interface FormattedChatMessage {
  conversationRole: 'model' | 'user';
  content: string;
  createdUtc: string;
  state?: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED';
  citations?: RetrievalCitation[];
  isPending?: boolean; // Flag to mark message as pending (waiting for response)
}

/**
 * Workbook chat request payload
 */
export interface WorkbookChatRequest {
  prompt: string;
  session_id?: string | null;
}

/**
 * Workbook chat response message
 */
export interface WorkbookChatResponseMessage {
  conversationRole: 'user' | 'model';
  createdUtc: string;
  id: string;
  query?: string;
  state?: string;
}

/**
 * Workbook chat response answer
 */
export interface WorkbookChatResponseAnswer {
  answer: string;
  citations: RetrievalCitation[];
  conversationRole: 'model';
  createdUtc: string;
  id: string;
  state: 'STATE_UNSPECIFIED' | 'IN_PROGRESS' | 'FAILED' | 'SUCCEEDED';
}

/**
 * Workbook chat response
 */
export interface WorkbookChatResponse {
  answer: WorkbookChatResponseAnswer;
  message: WorkbookChatResponseMessage;
  session_id: string;
}

/**
 * Result type for useMessageActions hook
 */
export interface UseMessageActionsResult {
  state: MessageActionsState;
  handleCopy: (e: React.MouseEvent) => void;
  handleThumbsUp: (e: React.MouseEvent) => void;
  handleThumbsDown: (e: React.MouseEvent) => void;
}

/**
 * State for message actions
 */
export interface MessageActionsState {
  copied: boolean;
  liked: boolean;
  disliked: boolean;
}

export interface MessageActionsProps {
  isHovered: boolean;
  feedback?: 0 | 1 | null;
  state: MessageActionsState;
  handlers: {
    handleCopy: (e: React.MouseEvent) => void;
    handleThumbsUp: (e: React.MouseEvent) => void;
    handleThumbsDown: (e: React.MouseEvent) => void;
  };
}

export type RetrievalWorkbookQueryRequest = {
  session_id?: string | null;
  prompt: string;
};

export type RetrievalWorkbookQueryResponse = {
  workbookSessionId: string;
  query: RetrievalSessionQuery;
  answer: RetrievalSessionAnswer;
};

export interface CitationRendererProps {
  message: string;
  citations?: RetrievalCitation[];
  citationSources?: RetrievalCitationSource[];
  isUser: boolean;
  textContentStyle: string;
}

export type RetrievalWorkbookMessageFeedbackRequest = {
  workbookId: string;
  sessionId: string;
  messageId: string;
  feedback: 0 | 1;
  isGlobal: boolean;
};

export type RetrievalWorkbookMessageFeedback = {
  workbookSessionId: string;
  answer: RetrievalSessionAnswer;
};

export type AddMockLoadingFile = {
  workbook: RetrievalWorkbook;
  file: RetrievalFile;
  isGlobal: boolean;
};

export type RemoveMockLoadingFile = {
  workbookId: string;
  fileName: string;
  isGlobal: boolean;
};
