import json
import sys
from datetime import datetime, timedelta, timezone
from typing import List, <PERSON>tableSequence, Optional, Tuple

from firebase_admin.firestore import firestore
from google.cloud.discoveryengine_v1 import (
    FactChunk,
    GenerateGroundedContentResponse,
    GroundedGenerationContent,
)

from lib.firestore_client import (
    FirestoreClient,
    FirestoreLimitException,
    FirestoreLimits,
)
from models.retrieval_workbook.RetrievalCitation import (
    RetrievalCitation,
    RetrievalCitationSource,
)
from models.retrieval_workbook.RetrievalEnums import RetrievalWorkbookChunkSize
from models.retrieval_workbook.RetrievalSession import (
    CreateRetrievalSession,
    CreateRetrievalSessionAnswer,
    CreateRetrievalSessionQuery,
    RetrievalSession,
    RetrievalSessionAnswer,
    RetrievalSessionMessage,
    RetrievalSessionQuery,
    RetrievalWorkbookMessage,
)
from models.retrieval_workbook.RetrievalWorkbook import (
    CreateRetrievalWorkbook,
    RetrievalWorkbook,
    UpdateRetrievalWorkbook,
)
from services.VertexAISearchService import VertexAISearchService
from website.config import get_config_val
from website.extensions import oidc_variables
from website.google_cloud_init import fetch_ENV_by_project_id


class RetrievalSessionService:
    # TODO, session & message deletes

    def __init__(
        self,
        persistence_client: firestore.Client,
        global_persistence_client: firestore.Client = None,
    ):
        self._persistence_client: firestore.Client = persistence_client
        self._global_persistence_client: firestore.Client = (
            global_persistence_client
            if global_persistence_client
            else FirestoreClient.GlobalInstanceClient()
        )

    def create_workbook_session(
        self,
        workbook_id: str,
        user_id: str,
        messages: List[RetrievalSessionMessage] = [],
    ) -> RetrievalSession:

        workbook_session = CreateRetrievalSession(user_id=user_id, messages=messages)

        document_string_size_bytes = sys.getsizeof(
            workbook_session.to_dict(date_format_iso=True)
        )
        if document_string_size_bytes >= FirestoreLimits.DOCUMENT_SIZE_BYTES:
            raise FirestoreLimitException.create_document_size_limit_exception(
                document_string_size_bytes
            )

        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        as_dict = workbook_session.to_dict(to_exclude={"messages": True})

        created_at, session_ref = sessions_ref.add(
            document_data=as_dict, document_id=workbook_session.id
        )

        to_return = RetrievalSession(
            id=session_ref.id, **workbook_session.to_dict(date_format_iso=False)
        )

        return to_return

    @staticmethod
    def to_pubsub_message(
        workbook_user: str,
        workbook_id: str,
        session_id: str,
        message: RetrievalSessionQuery | RetrievalSessionAnswer,
        is_public: bool = False,
    ) -> bytes:

        if isinstance(message, RetrievalSessionQuery):
            message_string = message.query
            has_citations = None
            model_feedback = None
        else:
            message_string = message.answer
            has_citations = True if (len(message.citations)) else False
            model_feedback = message.feedback

        RWM = RetrievalWorkbookMessage(
            message_id=message.id,
            workbook_session_id=session_id,
            workbook_id=workbook_id,
            workbook_user=workbook_user,
            message=message_string,
            message_role=message.conversation_role,
            created_utc=message.created_utc,
            model_feedback=model_feedback,
            has_citations=has_citations,
            is_public=is_public,
        )

        pubsub_message = RWM.to_pubsub_message()
        return pubsub_message

    def add_message(
        self,
        workbook_id: str,
        session_id: str,
        message: CreateRetrievalSessionQuery | CreateRetrievalSessionAnswer,
    ) -> RetrievalSessionQuery | RetrievalSessionAnswer:

        document_string_size_bytes = sys.getsizeof(
            message.to_dict(date_format_iso=True)
        )
        if document_string_size_bytes >= FirestoreLimits.DOCUMENT_SIZE_BYTES:
            raise FirestoreLimitException.create_document_size_limit_exception(
                document_string_size_bytes
            )

        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )

        messages_ref: firestore.CollectionReference = sessions_ref.document(
            session_id
        ).collection(RetrievalSessionMessage.COLLECTION_NAME)
        as_dict = message.to_dict()
        created_at, message_ref = messages_ref.add(
            document_data=as_dict, document_id=message.id
        )
        if as_dict["conversation_role"] == "user":
            message_doc = RetrievalSessionQuery(id=message_ref.id, **as_dict)
        else:
            message_doc = RetrievalSessionAnswer(id=message_ref.id, **as_dict)

        return message_doc

    def update_message(
        self,
        workbook_id: str,
        session_id: str,
        update_message: CreateRetrievalSessionQuery | CreateRetrievalSessionAnswer,
    ) -> RetrievalSessionQuery | RetrievalSessionAnswer:

        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        messages_ref: firestore.CollectionReference = sessions_ref.document(
            session_id
        ).collection(RetrievalSessionMessage.COLLECTION_NAME)

        # update_message should contain the messages id.
        as_dict = update_message.to_dict()
        write_result = messages_ref.document(update_message.id).update(as_dict)
        if as_dict["conversation_role"] == "user":
            message_doc = RetrievalSessionQuery(id=update_message.id, **as_dict)
        else:
            message_doc = RetrievalSessionAnswer(id=update_message.id, **as_dict)

        return message_doc

    def get_session_by_id(
        self, workbook_id: str, session_id: str
    ) -> Optional[RetrievalSession]:
        """ """
        session_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )

        session_doc = session_ref.document(session_id).get()
        if session_doc.exists:

            as_dict = session_doc.to_dict()
            return_session = RetrievalSession(id=session_doc.id, **as_dict)
            return_session.messages = self.get_messages_by_session_id(
                workbook_id=workbook_id, session_id=return_session.id
            )

            return return_session
        return None

    def get_sessions_by_workbook_id(
        self, workbook_id: str, with_messages: bool = False
    ) -> List[RetrievalSession]:
        """ """
        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )

        session_docs = sessions_ref.stream()
        sessions = []
        for doc in session_docs:
            if doc.exists:
                session_doc = RetrievalSession(id=doc.id, **(doc.to_dict()))
                if with_messages:
                    session_doc.messages = self.get_messages_by_session_id(
                        workbook_id=workbook_id, session_id=session_doc.id
                    )
                sessions.append(session_doc)
        return sessions

    def get_user_sessions_by_workbook_id(
        self, user_email: str, workbook_id: str
    ) -> List[RetrievalSession]:
        """ """
        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        sessions_user_query = sessions_ref.where("user_id", "==", user_email.lower())
        session_docs = sessions_user_query.stream()
        sessions = []
        for doc in session_docs:
            if doc.exists:
                session_doc = RetrievalSession(id=doc.id, **(doc.to_dict()))
                session_doc.messages = self.get_messages_by_session_id(
                    workbook_id=workbook_id, session_id=session_doc.id
                )
                sessions.append(session_doc)
        return sessions

    def get_messages_by_session_id(
        self, workbook_id: str, session_id: str
    ) -> List[RetrievalSessionQuery | RetrievalSessionAnswer]:
        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        messages_ref: firestore.CollectionReference = sessions_ref.document(
            session_id
        ).collection(RetrievalSessionMessage.COLLECTION_NAME)

        message_docs = messages_ref.order_by("created_utc").stream()
        messages = []
        for doc in message_docs:
            if doc.exists:
                as_dict = doc.to_dict()

                if as_dict["conversation_role"] == "user":
                    message_doc = RetrievalSessionQuery(id=doc.id, **as_dict)
                else:
                    message_doc = RetrievalSessionAnswer(id=doc.id, **as_dict)
                messages.append(message_doc)

        return messages

    def get_message_by_id(
        self, workbook_id: str, session_id: str, message_id: str
    ) -> RetrievalSessionQuery | RetrievalSessionAnswer:
        # probably for async stuff?

        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        messages_ref: firestore.CollectionReference = sessions_ref.document(
            session_id
        ).collection(RetrievalSessionMessage.COLLECTION_NAME)

        message_doc = messages_ref.document(message_id).get()
        if message_doc.exists:
            as_dict = message_doc.to_dict()
            if as_dict["conversation_role"] == "user":
                message_doc = RetrievalSessionQuery(id=message_doc.id, **as_dict)
            else:
                message_doc = RetrievalSessionAnswer(id=message_doc.id, **as_dict)
            return message_doc
        return None

    def delete_session_by_id(self, workbook_id: str, session_id: str):
        sessions_ref: firestore.CollectionReference = (
            self._persistence_client.collection(RetrievalWorkbook.COLLECTION_NAME)
            .document(workbook_id)
            .collection(RetrievalSession.COLLECTION_NAME)
        )
        session_ref: firestore.DocumentReference = sessions_ref.document(session_id)
        messages_ref: firestore.CollectionReference = session_ref.collection(
            RetrievalSessionMessage.COLLECTION_NAME
        )

        message_docs = messages_ref.stream()
        for message_doc in message_docs:
            message_ref = messages_ref.document(message_doc.id)
            message_ref.delete()
        session_ref.delete()

    @staticmethod
    def get_start_stop_index(answer_text: str, claim_text: str) -> Tuple[int, int]:
        start_index = answer_text.index(claim_text)
        end_index = start_index + len(claim_text)

        return (start_index, end_index)

    @staticmethod
    def make_citations(
        grounded_answer: str,
        support_chunks: MutableSequence[FactChunk],
        grounding_supports: MutableSequence[
            GenerateGroundedContentResponse.Candidate.GroundingMetadata.GroundingSupport
        ],
    ) -> Tuple[List[RetrievalCitation], List[RetrievalCitationSource]]:

        citations: List[RetrievalCitation] = []
        for support in grounding_supports:
            (start_index, end_index) = RetrievalSessionService.get_start_stop_index(
                grounded_answer, support.claim_text
            )
            support_score = support.support_score
            citation_sources = support.support_chunk_indices
            citations.append(
                RetrievalCitation(
                    start_index=start_index,
                    end_index=end_index,
                    support_score=support_score,
                    citation_sources=citation_sources,
                )
            )

        citation_sources: List[RetrievalCitationSource] = []
        for support_chunk in support_chunks:
            chunk_content = support_chunk.chunk_text
            gcs_path = support_chunk.source_metadata["uri"]
            name = support_chunk.source_metadata["title"]
            file_id = support_chunk.source_metadata["document_id"]

            citation_sources.append(
                RetrievalCitationSource(
                    file_id=file_id,
                    name=name,
                    gcs_path=gcs_path,
                    chunk_content=chunk_content,
                )
            )

        return (citations, citation_sources)

    def ask_message(
        self,
        workbook_id: str,
        prompt: str,
        domain: str,
        chunk_size: RetrievalWorkbookChunkSize,
        workbook_session_id: str,
        new_session: bool,
        workbook_system_instructions: str = None,
        workbook_temperature: float = None,
        is_global: bool = False,
    ):
        VAIS = VertexAISearchService(domain=domain, chunk_size=chunk_size)
        search_filter = VAIS.make_search_filter(
            domain="global" if is_global else domain, workbook_id=workbook_id
        )
        session_prompts: List[GroundedGenerationContent] = []
        if not new_session:
            x = -41
            last_X_prompts = self.get_messages_by_session_id(
                workbook_id=workbook_id, session_id=workbook_session_id
            )[x:-1]
            # -1 is because the current prompt is in firestore, so theres an odd amount of messages
            for X_prompt in last_X_prompts:
                if X_prompt.conversation_role == "user":
                    message = X_prompt.query
                else:
                    message = X_prompt.answer
                new_content = VAIS.form_content(X_prompt.conversation_role, message)
                session_prompts.append(new_content)

        response = VAIS.send_grounded_request(
            search_filter=search_filter,
            prompt=prompt,
            system_instructions=workbook_system_instructions,
            temperature=workbook_temperature,
            extra_contents=session_prompts,
        )
        candidate_data = VAIS.get_candidate_data(response)

        grounded_answer = candidate_data["response_text"]
        support_chunks = candidate_data["support_chunks"]
        grounding_supports = candidate_data["grounding_supports"]

        (citations, citation_sources) = self.make_citations(
            grounded_answer, support_chunks, grounding_supports
        )

        answer = CreateRetrievalSessionAnswer(
            state="SUCCEEDED",
            answer=grounded_answer,
            citations=citations,
            citation_sources=citation_sources,
        )
        created_answer = self.add_message(workbook_id, workbook_session_id, answer)

        return created_answer
