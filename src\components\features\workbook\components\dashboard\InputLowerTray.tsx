import React, { useState } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { IoMdSettings } from 'react-icons/io';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { InputLowerTrayProps, RetrievalWorkbook } from '@features/workbook/workbookTypes';
import SettingsModal, { getTemperatureDisplayName } from './SettingsModal';
import { TemperatureIcon } from '@/components/common/icons';
import './InputLowerTray.scss';

interface InputLowerTrayExtendedProps extends InputLowerTrayProps {
  workbook?: RetrievalWorkbook;
  onSettingsSave: (temperature: number) => Promise<void>;
  isUpdatingSettings: boolean;
  isGlobal?: boolean;
}

const InputLowerTray: React.FC<InputLowerTrayExtendedProps> = ({
  onSubmit,
  workbook,
  onSettingsSave,
  isUpdatingSettings,
  isGlobal = false,
}) => {
  const { classes } = useThemeStyles();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  const handleSettingsClick = () => {
    setIsSettingsModalOpen(true);
  };

  const handleSettingsModalClose = () => {
    setIsSettingsModalOpen(false);
  };

  // Get current temperature display name using the centralized function
  const currentTemperatureDisplay = workbook ? getTemperatureDisplayName(workbook.temperature) : 'Less Creative';

  return (
    <>
      <div className="input-lower-tray">
        <div className="input-lower-tray__button-group">
          {/* Temperature Badge - Hidden for public workbooks */}
          {!isGlobal && (
            <div className="input-lower-tray__temperature-badge">
              <TemperatureIcon className="input-lower-tray__temperature-icon" />
              <span className="input-lower-tray__temperature-text">{currentTemperatureDisplay}</span>
            </div>
          )}

          {/* Settings Button - Hidden for public workbooks */}
          {!isGlobal && (
            <button
              className={`input-lower-tray__settings-button ${classes.circleIconButton}`}
              onClick={handleSettingsClick}
              aria-label="Settings"
              tabIndex={0}
            >
              <IoMdSettings className="input-lower-tray__settings-icon" />
            </button>
          )}

          {/* Send Button */}
          <button
            className={`input-lower-tray__send-button ${classes.circleIconButton}`}
            onClick={onSubmit}
            aria-label="Send message"
            tabIndex={0}
          >
            <FaArrowRight className="input-lower-tray__send-icon" />
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={handleSettingsModalClose}
        onSave={onSettingsSave}
        workbook={workbook}
        isLoading={isUpdatingSettings}
        isGlobal={isGlobal}
      />
    </>
  );
};

export default InputLowerTray;
