.edit-workbook-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-workbook-modal {
  width: 800px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 32px;
  background: #003963;
  color: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.edit-workbook-modal__header {
  padding: 32px 48px 16px 48px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

.edit-workbook-modal__title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.edit-workbook-modal__content {
  padding: 0 48px 32px 48px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  gap: 32px;
}

.edit-workbook-modal__input-container {
  position: relative;
  width: 100%;
}

.edit-workbook-modal__input {
  width: 100%;
  padding: 12px 48px 12px 16px;
  border: 1px solid #0066B1;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #0080ff;
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
}

.edit-workbook-modal__input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  width: 24px;
  height: 24px;
}

.edit-workbook-modal__button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.edit-workbook-modal__button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.edit-workbook-modal__cancel-button {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
  }
}

.edit-workbook-modal__save-button {
  background: #0066B1;
  color: white;

  &:hover:not(:disabled) {
    background: #0080ff;
  }
}


