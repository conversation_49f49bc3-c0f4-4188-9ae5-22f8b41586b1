// Simplified CSS Variables - only what's actually used
:root {
  --primary-color: #0066b1;
  --modal-background: #003963;
  --background-dark: rgba(0, 45, 79, 1);
  --border-color: rgba(109, 120, 127, 1);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
}

/* ===== MODAL LAYOUT ===== */
.create-workbook-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 14, 26, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 20px;
}

.create-workbook {
  position: relative;
  width: min(800px, 85vw);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
  background: var(--modal-background);
  border-radius: 32px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);

  // Responsive breakpoints
  @media (max-width: 1366px) { width: min(600px, 75vw); max-height: 88vh; border-radius: 24px; }
  @media (max-width: 1200px) { width: min(500px, 70vw); border-radius: 20px; }
  @media (max-width: 1024px) { width: min(450px, 65vw); max-height: 92vh; border-radius: 16px; }
  @media (max-width: 768px) { width: min(400px, 60vw); max-height: 95vh; border-radius: 12px; }
  @media (max-height: 700px) { max-height: 85vh; }
}

/* ===== HEADER COMPONENTS ===== */
.create-workbook__close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover { background: rgba(255, 255, 255, 0.1); opacity: 0.8; }
  &:focus-visible { outline: 2px solid var(--text-primary); outline-offset: 2px; }
}

.create-workbook__content {
  flex: 1;
  padding: 32px 48px 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  @media (max-width: 1366px) { padding: 28px 36px 16px; }
  @media (max-width: 1200px) { padding: 24px 32px 16px; }
  @media (max-width: 1024px) { padding: 20px 24px 12px; }
  @media (max-height: 700px) { padding: 20px 28px 12px; }
}

.create-workbook__title {
  font-family: 'Sofia_Pro', sans-serif;
  font-size: 48px;
  font-weight: 500;
  line-height: 1.3;
  color: var(--text-primary);
  margin: 0 0 32px 0;

  @media (max-width: 1366px) { font-size: 42px; margin-bottom: 28px; }
  @media (max-width: 1200px) { font-size: 36px; margin-bottom: 24px; }
  @media (max-width: 1024px) { font-size: 32px; margin-bottom: 20px; }
  @media (max-height: 700px) { font-size: 32px; margin-bottom: 20px; }
}

/* ===== FORM COMPONENTS ===== */
// Shared field styles
%field-base {
  width: 100%;
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-family: 'Roboto', sans-serif;
  outline: none;
  transition: border-color 0.2s ease;

  &::placeholder { color: var(--text-muted); }
  &:focus { border-color: var(--primary-color); }
  &--with-icon { padding-right: 48px; }
}

// Shared icon styles
%field-icon {
  position: absolute;
  right: 12px;
  pointer-events: none;
  transition: color 0.2s ease;

  &--inactive { color: var(--text-muted); }
  &--active { color: var(--text-primary); }
}

.input-group, .textarea-group {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.input-field {
  @extend %field-base;
  padding: 12px 16px;
  font-size: 16px;
}

.input-field__icon {
  @extend %field-icon;
  top: 50%;
  transform: translateY(-50%);
}

.textarea-field {
  @extend %field-base;
  height: 144px;
  padding: 8px 24px 24px;
  font-size: 14px;
  resize: none;

  @media (max-height: 700px) { height: 100px; }
}

.textarea-field__icon {
  @extend %field-icon;
  top: 12px;
}

// Section components
.section-title {
  font-family: 'Sofia_Pro', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  height: 30px;
  display: flex;
  align-items: center;
}

.system-instructions {
  .section-title { margin-bottom: 0; }

  .section-description {
    width: 450px;
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    line-height: 1.2;
    color: var(--text-secondary);
    margin: 0 0 8px 0;

    @media (max-width: 900px) { width: 100%; max-width: 450px; }
  }
}

/* ===== RADIO COMPONENTS ===== */
.chunk-size-section {
  margin-bottom: 16px;

  .section-title {
    font-weight: 500;
    margin-bottom: 12px;
  }
}

.chunk-size-radio-group {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 44px;
  padding: 4px;

  &__option {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-primary);
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    cursor: pointer;
  }

  @media (max-width: 900px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

.radio-item {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px solid #ffffff;
  background-color: transparent;
  transition: background-color 0.2s ease;

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  &[data-unchecked] { display: none; }

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-primary);
  }
}

/* ===== FILE UPLOAD COMPONENTS ===== */
.file-upload-section {
  margin-bottom: 32px;

  @media (max-height: 700px) { margin-bottom: 20px; }
}

.file-upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 110px;
  height: 45px;
  padding: 12px;
  background: var(--primary-color);
  border: none;
  border-radius: 4px;
  color: var(--text-primary);
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover { background-color: #0052a3; }
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: var(--background-dark);
  border: 1px solid var(--primary-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  min-width: 0;

  &__icon {
    flex-shrink: 0;
    margin-right: 8px;
    display: flex;
    align-items: center;
  }

  &__name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 12px;
    min-width: 0;
  }

  &__delete-btn {
    flex-shrink: 0;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    transition: color 0.2s ease;

    &:hover { color: #ff6b6b; }
    &:focus-visible {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
      border-radius: 2px;
    }
  }
}

/* ===== ACTIONS & FOOTER ===== */
.form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}

.btn {
  display: flex;
  align-items: center;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  outline: none;

  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  &--secondary {
    background: transparent;
    color: var(--text-primary);
    padding: 8px 16px;

    &:hover { opacity: 0.8; }
  }

  &--primary {
    gap: 8px;
    padding: 12px 24px;
    background: transparent;
    border: 2px solid var(--primary-color);
    border-radius: 50px;
    color: var(--text-primary);

    &:hover:not(.btn--disabled) {
      background-color: var(--primary-color);
    }

    &.btn--disabled {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover { background-color: transparent; }
    }
  }
}

.create-workbook__footer {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 48px;
  border-top: 1px solid rgba(0, 102, 177, 0.3);
  color: var(--text-secondary);
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  width: 100%;
  flex-shrink: 0;

  @media (max-width: 1366px) { padding: 14px 36px; }
  @media (max-width: 1200px) { padding: 12px 32px; }
  @media (max-width: 1024px) { padding: 10px 24px; font-size: 11px; }
  @media (max-height: 700px) { padding: 10px 28px; }
}
