import React, { useRef, useState, useEffect } from 'react';

import { useAppDispatch, useAppSelector } from '@/store/hooks';

import { PromptInput } from '@/components/features/chat';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { useAutoScroll } from '@/components/common/hooks/useAutoScroll';
import ChatMessageList from '../../components/chat/ChatMessageList';
import InputLowerTray from '../../components/dashboard/InputLowerTray';
import { patchWorkbookById } from '@/api/workbookApi';

import {
  selectLatestWorkbookSession,
  selectUserWorkbookSessionPendingStatus,
  selectGlobalWorkbookSessionPendingStatus,
  selectWorkbookQueryInProgress,
  askMyWorkbookById,
  askGlobalWorkbookById,
} from '@/store/slices/workbookSlice';
import { RetrievalWorkbook } from '@/types';

import './ChatView.scss';

export type WorkbookChatViewProps = {
  workbook: RetrievalWorkbook;
  isGlobal: boolean;
};

/**
 * WorkbookChatView - Main component for the workbook chat interface
 *
 * - Manages the conversation state using custom hooks
 * - Handles sending messages to the API
 * - Renders the chat messages and input box
 * - Auto-scrolls to newest messages when conversation updates using useAutoScroll hook
 * - Handles initial message sending if provided through props
 */
const WorkbookChatView: React.FC<WorkbookChatViewProps> = (props: WorkbookChatViewProps) => {
  const { workbook: propsWorkbook, isGlobal } = props;

  const [promptText, setPromptText] = useState('');
  const { classes, colors } = useThemeStyles();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();

  const [workbook, setWorkbook] = useState<RetrievalWorkbook>(propsWorkbook);
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);

  useEffect(() => {
    setWorkbook(propsWorkbook);
  }, [propsWorkbook]);

  const session = useAppSelector(state => selectLatestWorkbookSession(state, workbook?.id, isGlobal));
  const sessionPendingStatus = useAppSelector(state =>
    isGlobal
      ? selectGlobalWorkbookSessionPendingStatus(state, workbook.id, session?.id ?? '')
      : selectUserWorkbookSessionPendingStatus(state, workbook.id, session?.id ?? '')
  );

  const globalQueryInProgress = useAppSelector(selectWorkbookQueryInProgress);

  const queryInProgress = sessionPendingStatus?.isQuerying ?? globalQueryInProgress;

  useAutoScroll({
    messages: session?.messages ?? [],
    isLoading: queryInProgress,
    messagesContainerRef,
    scrollTargetRef: messagesEndRef,
  });

  const handleSettingsSave = async (temperature: number) => {
    if (!workbook) return;

    setIsUpdatingSettings(true);
    try {
      const response = await patchWorkbookById({ id: workbook.id, name: workbook.name, temperature }, isGlobal);
      setWorkbook(prevWorkbook => ({
        ...prevWorkbook,
        temperature,
        updatedUtc: new Date(response.updatedUtc),
      }));
    } catch (error) {
      console.error('Failed to update workbook settings:', error);
    } finally {
      setIsUpdatingSettings(false);
    }
  };

  // Styles
  const styles = {
    container:
      'flex flex-col w-full mx-auto max-w-4xl relative gap-[24px] h-full overflow-x-hidden font-roboto text-[18px]',
    inputContainer: `
      flex flex-col justify-between items-center 
      rounded-[32px] border border-[${colors.border}] 
      ${classes.backgroundInput} px-6 py-3 pb-14
      w-full
      overflow-y-visible overflow-x-hidden
      relative
      max-h-[250px]
      mb-4
    `,
  };

  const handlePromptInputChange = async (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPromptText(event.currentTarget.value);
  };

  const handlePromptSend = async (promptText: string) => {
    if (workbook?.id) {
      const askAction = isGlobal ? askGlobalWorkbookById : askMyWorkbookById;
      dispatch(
        askAction({
          workbookId: workbook.id,
          workbookQuery: {
            session_id: session?.id ?? null,
            prompt: promptText.trim(),
          },
        })
      );
    }
    setPromptText('');
  };

  return (
    <div className={styles.container}>
      {/* Chat messages */}
      <div ref={messagesContainerRef} className={`chat-messages-container`}>
        <ChatMessageList
          workbook={workbook}
          session={session}
          messages={session?.messages ?? []}
          loading={queryInProgress}
          isGlobal={isGlobal}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Input area */}
      <div className={styles.inputContainer}>
        <PromptInput
          message={promptText}
          onChange={handlePromptInputChange}
          onSubmit={() => handlePromptSend(promptText)}
        />
        <InputLowerTray
          textColor={classes.text}
          onSubmit={() => handlePromptSend(promptText)}
          workbook={workbook}
          onSettingsSave={handleSettingsSave}
          isUpdatingSettings={isUpdatingSettings}
          isGlobal={isGlobal}
        />
      </div>
    </div>
  );
};

export default WorkbookChatView;
